package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IMeService interface {
	GetProfile(userID string) (*models.User, core.IError)
	UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError)
	GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError)
	DeleteDevice(userID, tokenID string) core.IError
}

type meService struct {
	ctx core.IContext
}

type MeUpdatePayload struct {
	FullName    string
	DisplayName string
	Position    string
	Team        string
	AvatarURL   string
}

func (s meService) GetProfile(userID string) (*models.User, core.IError) {
	user, ierr := repo.User(s.ctx).FindOne("id = ?", userID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return user, nil
}

func (s meService) UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError) {
	user, ierr := s.GetProfile(userID)
	if ierr != nil {
		return nil, ierr
	}

	// Update fields if provided
	if input.FullName != "" {
		user.FullName = input.FullName
	}
	if input.DisplayName != "" {
		user.DisplayName = input.DisplayName
	}
	if input.Position != "" {
		user.Position = input.Position
	}
	if input.Team != "" {
		user.Team = utils.ToPointer(input.Team)
	}
	if input.AvatarURL != "" {
		user.AvatarURL = input.AvatarURL
	}

	ierr = repo.User(s.ctx).Where("id = ?", userID).Updates(user)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.GetProfile(userID)
}

func (s meService) GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError) {
	return repo.UserToken(s.ctx, repo.UserTokenOrderBy(pageOptions)).Where("user_id = ?", userID).Pagination(pageOptions)
}

func (s meService) DeleteDevice(userID, tokenID string) core.IError {
	// Verify the token belongs to the user
	_, ierr := repo.UserToken(s.ctx).FindOne("id = ? AND user_id = ?", tokenID, userID)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.UserToken(s.ctx).Delete("id = ? AND user_id = ?", tokenID, userID)
}

func NewMeService(ctx core.IContext) IMeService {
	return &meService{ctx: ctx}
}
