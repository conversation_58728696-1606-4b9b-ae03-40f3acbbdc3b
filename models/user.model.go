package models

type User struct {
	BaseModelHardDelete
	Email       string  `json:"email" gorm:"column:email"`
	FullName    string  `json:"full_name" gorm:"column:full_name"`
	DisplayName string  `json:"display_name" gorm:"column:display_name"`
	Position    string  `json:"position" gorm:"column:position"`
	Team        *string `json:"team" gorm:"column:team"`
	Company     *string  `json:"company" gorm:"column:company"`
	AvatarURL   string  `json:"avatar_url" gorm:"column:avatar_url"`
	SlackID     *string `json:"slack_id" gorm:"column:slack_id"`
	Role        string  `json:"role" gorm:"column:role"`
	IsActive    bool    `json:"is_active" gorm:"column:is_active"`
}

func (User) TableName() string {
	return "users"
}
