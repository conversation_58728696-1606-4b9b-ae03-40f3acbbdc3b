package requests

import core "gitlab.finema.co/finema/idin-core"

type UserUpdate struct {
	core.BaseValidator
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	Team        *string `json:"team"`
	AvatarURL   *string `json:"avatar_url"`
	Role        *string `json:"role"`
}

func (r *UserUpdate) Valid(ctx core.IContext) core.IError {
	// All fields are optional for updates
	return r.Error()
}
