package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email       *string `json:"email"`
	Password    *string `json:"password"`
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	Team        *string `json:"team"`
	AvatarURL   *string `json:"avatar_url"`
	Role        *string `json:"role"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>mail(r.<PERSON>ail, "email"))
	r.Must(r.<PERSON>equired(r.<PERSON>ail, "email"))
	r.Must(r.IsStrUnique(ctx, r.<PERSON>ail, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.<PERSON>equired(r.Password, "password"))
	r.Must(r.<PERSON>quired(r.<PERSON>, "full_name"))

	return r.<PERSON>()
}
