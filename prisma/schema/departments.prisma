model departments {
    id            String    @id @default(uuid()) @db.Uuid
    created_at    DateTime  @default(now())
    updated_at    DateTime  @updatedAt
    deleted_at    DateTime?
    ministry_id   String    @db.Uuid
    name_th       String
    name_en       String?
    short_name_th String?
    short_name_en String?
    code          Int?

    ministry  ministries  @relation(fields: [ministry_id], references: [id], onDelete: Cascade)
    projects  projects[]
    divisions divisions[]

    @@index([ministry_id])
    @@index([name_th])
}
